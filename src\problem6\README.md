# Live Scoreboard API Service Module

## Overview

This module provides a real-time scoreboard system that displays the top 10 user scores with live updates. The system ensures secure score updates through proper authentication and authorization mechanisms while providing real-time data synchronization across all connected clients.

## Architecture Components

### Core Components

1. **Score Management Service** - Handles score updates and validation
2. **Real-time Communication Layer** - WebSocket-based live updates
3. **Leaderboard Cache** - High-performance score ranking
4. **Database Layer** - Persistent score storage

### Security Components

5. **Authentication & Authorization** - Secure user verification
6. **Rate Limiting** - Anti-abuse protection

## API Endpoints

### 1. Score Update Endpoint
```
POST /api/scores/update
```

**Purpose**: Update user score after completing an action

**Headers**:
- `Authorization: Bearer <jwt_token>`
- `Content-Type: application/json`

**Request Body**:
```json
{
  "actionId": "string",
  "scoreIncrement": "number",
  "timestamp": "ISO8601",
  "actionSignature": "string"
}
```

**Response**:
```json
{
  "success": true,
  "newScore": 1250,
  "rank": 5,
  "message": "Score updated successfully"
}
```

### 2. Leaderboard Retrieval
```
GET /api/leaderboard/top10
```

**Purpose**: Get current top 10 scores

**Response**:
```json
{
  "leaderboard": [
    {
      "rank": 1,
      "userId": "user123",
      "username": "player1",
      "score": 2500,
      "lastUpdated": "2024-01-15T10:30:00Z"
    },
    // ... complete top 10

  ],
  "lastRefresh": "2024-01-15T10:30:00Z"
}
```

### 3. User Score Retrieval
```
GET /api/users/{userId}/score
```

**Purpose**: Get current user's score and basic stats

**Headers**:
- `Authorization: Bearer <jwt_token>`

**Response**:
```json
{
  "id": 123,
  "username": "player1",
  "total_score": 1250,
  "created_at": "2024-01-10T08:00:00Z",
  "last_score_update": "2024-01-15T10:30:00Z"
}
```

### 4. WebSocket Connection
```
WS /ws/leaderboard
```

**Purpose**: Real-time leaderboard updates

**Authentication**: JWT token via query parameter or header

**Events**:
- `leaderboard_update`: Broadcast to all clients when any top 10 user's score changes
- `user_score_update`: Sent to specific user when their score changes (includes rank information)

## Security Measures

### 1. Authentication
- JWT-based user authentication
- Token expiration and refresh mechanism
- Secure token storage recommendations

### 2. Authorization
- Action-based permissions
- User role verification
- Score increment validation

### 3. Anti-Fraud Protection
- **Action Signature Verification**: Each score update must include a cryptographic signature
- **Rate Limiting**: Maximum score updates per user per time window
- **Anomaly Detection**: Unusual scoring patterns flagged for review
- **Action Validation**: Server-side verification of completed actions

### 4. Input Validation
- Score increment bounds checking
- Timestamp validation (prevent replay attacks)
- Action ID verification against valid actions

## Data Models

### User Score Model
```javascript
{
  userId: String,
  username: String,
  totalScore: Number,
  lastActionTimestamp: Date,
  actionHistory: [{
    actionId: String,
    scoreGained: Number,
    timestamp: Date,
    verified: Boolean
  }]
}
```

### Leaderboard Cache Model
```javascript
{
  rank: Number,
  userId: String,
  username: String,
  score: Number,
  lastUpdated: Date
}
```

## Real-time Updates Flow

1. User completes action on frontend
2. Frontend generates action signature
3. API call sent to `/api/scores/update`
4. Server validates authentication and action
5. Score updated in database
6. **Always send `user_score_update` to the specific user** (regardless of ranking)
7. Check if user is in top 10
8. If user in top 10: update cache and broadcast `leaderboard_update` to all connected clients
9. Frontend updates user's personal score and leaderboard display

## Performance Considerations

### Caching Strategy
- **Redis Cache**: Top 10 leaderboard cached for fast retrieval (read-through + write-through)
- **Cache TTL**: 5 minutes for leaderboard data

### Database Optimization
- **Indexed Queries**: Optimized for score-based sorting
- **Read Replicas**: Separate read/write operations
- **Batch Updates**: Group multiple score updates when possible

### WebSocket Management
- **Connection Pooling**: Efficient connection management
- **Message Queuing**: Handle high-frequency updates
- **Graceful Degradation**: Fallback to polling if WebSocket fails

### Broadcasting Strategy

#### Individual User Updates (Always Sent)
- **Target**: Specific user who performed the action
- **Trigger**: Every score update, regardless of ranking
- **Purpose**: Immediate feedback and score confirmation
- **Event Type**: `user_score_update`

#### Leaderboard Updates (Conditional Broadcast)
- **Target**: All connected clients
- **Trigger**: When any top 10 user's score changes (regardless of rank change)
- **Purpose**: Keep leaderboard display current with latest scores
- **Event Type**: `leaderboard_update`


## Error Handling

### Common Error Responses
```json
{
  "error": {
    "code": "INVALID_ACTION",
    "message": "Action signature verification failed",
    "details": "The provided action signature is invalid or expired"
  }
}
```

### Error Codes
- `UNAUTHORIZED`: Invalid or expired authentication
- `INVALID_ACTION`: Action verification failed
- `RATE_LIMITED`: Too many requests
- `INVALID_SCORE`: Score increment out of bounds
- `SERVER_ERROR`: Internal server error

## Monitoring & Logging

### Key Metrics
- Score update frequency per user
- WebSocket connection count
- API response times
- Cache hit/miss ratios
- Failed authentication attempts

### Logging Requirements
- All score updates with user ID and timestamp
- Failed authentication attempts
- Suspicious activity patterns
- System performance metrics

## Deployment Considerations

### Scaling Recommendations
- **Horizontal Scaling**: Multiple API server instances
- **Load Balancing**: Distribute WebSocket connections
- **Caching Layer**: Distributed Redis cache
- **Database**: Read replicas for high availability

## Technical Implementation Details

### Database Schema

#### User Score Table
```sql
CREATE TABLE user_score (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    total_score INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_user_score_total ON user_score(total_score DESC);
```

#### Score Updates Table
```sql
CREATE TABLE score_updates (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES user_score(id),
    score_increment INTEGER NOT NULL,
    action_signature VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_score_updates_user ON score_updates(user_id);
CREATE INDEX idx_score_updates_time ON score_updates(created_at DESC);
```

### WebSocket Event Specifications

#### Leaderboard Update Event
```json
{
  "type": "leaderboard_update",
  "data": {
    "leaderboard": [
      {
        "rank": 1,
        "id": 456,
        "username": "newleader",
        "total_score": 2550,
        "score_change": "+100"
      },
      {
        "rank": 2,
        "id": 123,
        "username": "player1",
        "total_score": 2500,
        "score_change": "+0"
      },
      {
        "rank": 3,
        "id": 789,
        "username": "player3",
        "total_score": 2400,
        "score_change": "+25"
      },
      {
        "rank": 4,
        "id": 234,
        "username": "player4",
        "total_score": 2300,
        "score_change": "+10"
      },
      {
        "rank": 5,
        "id": 567,
        "username": "player5",
        "total_score": 2200,
        "score_change": "+5"
      }
      // ... complete top 10

    ],
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

**Note**: When rankings change, always send the **complete top 10 array** to all connected clients. This ensures:
- Simple frontend implementation (just replace entire leaderboard)
- Consistent state across all clients
- Handles all ranking scenarios (new entries, position swaps, ties)
- Minimal network overhead (only 10 records maximum)

#### User Score Update Event
```json
{
  "type": "user_score_update",
  "data": {
    "id": 123,
    "username": "player1",
    "total_score": 1250,
    "score_increment": 25,
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

