
## 📁 Project Structure

```
src/problem5/
├── server.js              # Main server file
├── configs/               # Configuration files
│   └── database.js        # SQLite database config & initialization
├── services/              # Business logic
│   └── resourceService.js
├── controllers/           # HTTP request handlers  
│   └── resourceController.js
├── routes/               # API route definitions
│   └── resourceRoutes.js
├── test/                 # Chai HTTP tests
│   └── api.test.js
├── data/                 # Database files (auto-created)
│   └── resources.db      # SQLite database
├── package.json
└── README.md
```

## 🚀 Getting Started

### Install Dependencies
```bash
npm install
```

### Start the Server
```bash
npm start
```
Server runs on `http://localhost:3000`

**Database Auto-Initialization:**
- SQLite database file is automatically created in `data/resources.db`
- Tables are created automatically on first run
- No manual database setup required!

### Run Tests
```bash
npm test
```

## 📋 API Endpoints

### Health Check
- **GET** `/health` - Check if server is running

### Resources
- **GET** `/api/resources` - Get all resources
- **GET** `/api/resources/:id` - Get resource by ID
- **POST** `/api/resources` - Create new resource
- **PUT** `/api/resources/:id` - Update resource
- **DELETE** `/api/resources/:id` - Delete resource

### Query Parameters (GET /api/resources)
- `category` - Filter by category
- `status` - Filter by status (active/inactive)
- `search` - Search in name and description

## 📝 Complete Usage Examples

### 🔍 Health Check
```bash
# Check if server is running
curl http://localhost:3000/health
```
**Response:**
```json
{
  "success": true,
  "message": "Server is running"
}
```

### 📋 Get All Resources

#### Basic Request
```bash
curl http://localhost:3000/api/resources
```

#### With PowerShell (Windows)
```powershell
Invoke-RestMethod -Uri "http://localhost:3000/api/resources" -Method GET
```

#### With JavaScript (fetch)
```javascript
fetch('http://localhost:3000/api/resources')
  .then(response => response.json())
  .then(data => console.log(data));
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Sample Resource",
      "description": "This is a sample resource",
      "category": "General",
      "status": "active",
      "created_at": "2024-01-15 10:30:00",
      "updated_at": "2024-01-15 10:30:00"
    }
  ],
  "pagination": {
    "total": 1,
    "count": 1
  }
}
```

### 🔍 Get Resource by ID

#### Basic Request
```bash
curl http://localhost:3000/api/resources/1
```

#### With PowerShell
```powershell
Invoke-RestMethod -Uri "http://localhost:3000/api/resources/1" -Method GET
```

#### With JavaScript
```javascript
fetch('http://localhost:3000/api/resources/1')
  .then(response => response.json())
  .then(data => console.log(data));
```

**Success Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Sample Resource",
    "description": "This is a sample resource",
    "category": "General",
    "status": "active",
    "created_at": "2024-01-15 10:30:00",
    "updated_at": "2024-01-15 10:30:00"
  }
}
```

**Error Response (404):**
```json
{
  "success": false,
  "error": "Resource not found"
}
```

### ➕ Create New Resource

#### Basic Creation
```bash
curl -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My New Resource",
    "description": "A detailed description of my resource"
  }'
```

#### With All Fields
```bash
curl -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Complete Resource",
    "description": "A resource with all fields specified",
    "category": "Documentation",
    "status": "active"
  }'
```

#### With PowerShell
```powershell
$body = @{
    name = "PowerShell Resource"
    description = "Created using PowerShell"
    category = "Automation"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3000/api/resources" -Method POST -Body $body -ContentType "application/json"
```

#### With JavaScript
```javascript
fetch('http://localhost:3000/api/resources', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: 'JavaScript Resource',
    description: 'Created using JavaScript fetch',
    category: 'Web Development'
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

#### With Python
```python
import requests

data = {
    "name": "Python Resource",
    "description": "Created using Python requests",
    "category": "Data Science"
}

response = requests.post('http://localhost:3000/api/resources', json=data)
print(response.json())
```

**Success Response (201):**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "name": "My New Resource",
    "description": "A detailed description of my resource",
    "category": "General",
    "status": "active",
    "created_at": "2024-01-15 11:00:00",
    "updated_at": "2024-01-15 11:00:00"
  },
  "message": "Resource created successfully"
}
```

**Validation Error Response (400):**
```json
{
  "success": false,
  "error": "Name is required"
}
```

### ✏️ Update Resource

#### Partial Update
```bash
curl -X PUT http://localhost:3000/api/resources/1 \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Resource Name"
  }'
```

#### Complete Update
```bash
curl -X PUT http://localhost:3000/api/resources/1 \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Completely Updated Resource",
    "description": "This resource has been completely updated",
    "category": "Updated Category",
    "status": "inactive"
  }'
```

#### With PowerShell
```powershell
$updateData = @{
    name = "PowerShell Updated"
    description = "Updated via PowerShell"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3000/api/resources/1" -Method PUT -Body $updateData -ContentType "application/json"
```

#### With JavaScript
```javascript
fetch('http://localhost:3000/api/resources/1', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: 'Updated via JavaScript',
    status: 'inactive'
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

**Success Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Updated Resource Name",
    "description": "Original description",
    "category": "General",
    "status": "active",
    "created_at": "2024-01-15 10:30:00",
    "updated_at": "2024-01-15 11:15:00"
  },
  "message": "Resource updated successfully"
}
```

**Error Response (404):**
```json
{
  "success": false,
  "error": "Resource not found"
}
```

### 🗑️ Delete Resource

#### Basic Delete
```bash
curl -X DELETE http://localhost:3000/api/resources/1
```

#### With PowerShell
```powershell
Invoke-RestMethod -Uri "http://localhost:3000/api/resources/1" -Method DELETE
```

#### With JavaScript
```javascript
fetch('http://localhost:3000/api/resources/1', {
  method: 'DELETE'
})
.then(response => response.json())
.then(data => console.log(data));
```

**Success Response:**
```json
{
  "success": true,
  "message": "Resource deleted successfully"
}
```

**Error Response (404):**
```json
{
  "success": false,
  "error": "Resource not found"
}
```

### 🔍 Advanced Filtering & Search

#### Filter by Category
```bash
curl "http://localhost:3000/api/resources?category=Documentation"
```

#### Filter by Status
```bash
curl "http://localhost:3000/api/resources?status=active"
```

#### Search in Name and Description
```bash
curl "http://localhost:3000/api/resources?search=sample"
```

#### Combined Filters
```bash
curl "http://localhost:3000/api/resources?category=Documentation&status=active&search=guide"
```

#### URL Encoded Parameters
```bash
curl "http://localhost:3000/api/resources?search=my%20resource&category=Web%20Development"
```

#### With PowerShell
```powershell
$params = @{
    category = "Documentation"
    status = "active"
    search = "tutorial"
}
$queryString = ($params.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
Invoke-RestMethod -Uri "http://localhost:3000/api/resources?$queryString" -Method GET
```

#### With JavaScript
```javascript
const params = new URLSearchParams({
  category: 'Documentation',
  status: 'active',
  search: 'tutorial'
});

fetch(`http://localhost:3000/api/resources?${params}`)
  .then(response => response.json())
  .then(data => console.log(data));
```

**Filtered Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 3,
      "name": "Tutorial Guide",
      "description": "A comprehensive tutorial guide",
      "category": "Documentation",
      "status": "active",
      "created_at": "2024-01-15 09:00:00",
      "updated_at": "2024-01-15 09:00:00"
    }
  ],
  "pagination": {
    "total": 1,
    "count": 1
  }
}
```

### **Database Schema**
```sql
CREATE TABLE resources (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL DEFAULT 'General',
    status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'inactive')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 🚨 Error Handling Examples

#### Invalid JSON Format
```bash
curl -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{"name": "Invalid JSON"'  # Missing closing brace
```

#### Missing Required Fields
```bash
curl -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{
    "description": "Missing name field"
  }'
```

#### Empty Name Field
```bash
curl -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{
    "name": "",
    "description": "Empty name"
  }'
```

#### Invalid Status Value
```bash
curl -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Resource",
    "description": "Testing invalid status",
    "status": "invalid_status"
  }'
```

### 📊 Batch Operations Examples

#### Create Multiple Resources
```bash
# Create first resource
curl -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Resource 1",
    "description": "First resource in batch",
    "category": "Batch"
  }'

# Create second resource
curl -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Resource 2",
    "description": "Second resource in batch",
    "category": "Batch"
  }'

# Create third resource
curl -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Resource 3",
    "description": "Third resource in batch",
    "category": "Batch"
  }'
```

#### Batch Operations with Shell Script
```bash
#!/bin/bash
# batch_create.sh

resources=(
  '{"name":"API Documentation","description":"Complete API documentation","category":"Documentation"}'
  '{"name":"User Guide","description":"User guide for the application","category":"Documentation"}'
  '{"name":"Developer Setup","description":"Setup guide for developers","category":"Development"}'
)

for resource in "${resources[@]}"; do
  echo "Creating resource: $resource"
  curl -X POST http://localhost:3000/api/resources \
    -H "Content-Type: application/json" \
    -d "$resource"
  echo -e "\n---"
done
```

### 🔄 Workflow Examples

#### Complete CRUD Workflow
```bash
# 1. Create a resource
RESOURCE_ID=$(curl -s -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Workflow Resource",
    "description": "Testing complete workflow",
    "category": "Testing"
  }' | jq -r '.data.id')

echo "Created resource with ID: $RESOURCE_ID"

# 2. Read the resource
curl -s http://localhost:3000/api/resources/$RESOURCE_ID | jq '.'

# 3. Update the resource
curl -s -X PUT http://localhost:3000/api/resources/$RESOURCE_ID \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Workflow Resource",
    "status": "inactive"
  }' | jq '.'

# 4. Verify the update
curl -s http://localhost:3000/api/resources/$RESOURCE_ID | jq '.'

# 5. Delete the resource
curl -s -X DELETE http://localhost:3000/api/resources/$RESOURCE_ID | jq '.'

# 6. Verify deletion (should return 404)
curl -s http://localhost:3000/api/resources/$RESOURCE_ID | jq '.'
```

#### Search and Filter Workflow
```bash
# Create test data
curl -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{"name":"Active Tutorial","description":"An active tutorial resource","category":"Education","status":"active"}'

curl -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{"name":"Inactive Guide","description":"An inactive guide resource","category":"Education","status":"inactive"}'

curl -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{"name":"Development Tool","description":"A tool for development","category":"Tools","status":"active"}'

# Search for "tutorial" in name/description
echo "=== Search for 'tutorial' ==="
curl -s "http://localhost:3000/api/resources?search=tutorial" | jq '.data'

# Filter by Education category
echo "=== Filter by Education category ==="
curl -s "http://localhost:3000/api/resources?category=Education" | jq '.data'

# Filter by active status
echo "=== Filter by active status ==="
curl -s "http://localhost:3000/api/resources?status=active" | jq '.data'

# Combined filters
echo "=== Education + Active ==="
curl -s "http://localhost:3000/api/resources?category=Education&status=active" | jq '.data'
```

### 🧪 Testing Examples

#### Manual Testing Script
```bash
#!/bin/bash
# test_api.sh

BASE_URL="http://localhost:3000"

echo "🔍 Testing Health Check..."
curl -s "$BASE_URL/health" | jq '.'

echo -e "\n📋 Testing Get All Resources (should be empty)..."
curl -s "$BASE_URL/api/resources" | jq '.'

echo -e "\n➕ Testing Create Resource..."
RESPONSE=$(curl -s -X POST "$BASE_URL/api/resources" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Resource",
    "description": "A test resource for manual testing",
    "category": "Testing"
  }')
echo "$RESPONSE" | jq '.'
RESOURCE_ID=$(echo "$RESPONSE" | jq -r '.data.id')

echo -e "\n🔍 Testing Get Resource by ID..."
curl -s "$BASE_URL/api/resources/$RESOURCE_ID" | jq '.'

echo -e "\n✏️ Testing Update Resource..."
curl -s -X PUT "$BASE_URL/api/resources/$RESOURCE_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Test Resource",
    "status": "inactive"
  }' | jq '.'

echo -e "\n🗑️ Testing Delete Resource..."
curl -s -X DELETE "$BASE_URL/api/resources/$RESOURCE_ID" | jq '.'

echo -e "\n❌ Testing 404 Error..."
curl -s "$BASE_URL/api/resources/999" | jq '.'

echo -e "\n❌ Testing Validation Error..."
curl -s -X POST "$BASE_URL/api/resources" \
  -H "Content-Type: application/json" \
  -d '{
    "description": "Missing name field"
  }' | jq '.'

echo -e "\n✅ All tests completed!"
```

## 🧪 Testing

The project includes comprehensive Chai HTTP tests covering:
- ✅ Health check
- ✅ CRUD operations (Create, Read, Update, Delete)
- ✅ Input validation
- ✅ Error handling (404, 400)
- ✅ Resource filtering
- ✅ Database operations

Run tests with: `npm test`

