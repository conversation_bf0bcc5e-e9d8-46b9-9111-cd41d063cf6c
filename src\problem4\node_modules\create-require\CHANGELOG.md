# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [1.1.1](https://github.com/nuxt-contrib/create-require/compare/v1.1.0...v1.1.1) (2020-11-26)


### Bug Fixes

* **types:** explicitly import the URL type ([#3](https://github.com/nuxt-contrib/create-require/issues/3)) ([66a98cc](https://github.com/nuxt-contrib/create-require/commit/66a98cc60a430c3689f11ad111c5fbf4574a37b6))

## [1.1.0](https://github.com/nuxt-contrib/create-require/compare/v1.0.2...v1.1.0) (2020-11-21)


### Features

* fallback to process.cwd() if no filename provided either ([ae5e0d6](https://github.com/nuxt-contrib/create-require/commit/ae5e0d665945b980b82ae6e998146c32295a6734))

### [1.0.2](https://github.com/nuxt-contrib/create-require/compare/v1.0.1...v1.0.2) (2020-06-12)


### Bug Fixes

* use fake path if filename is directory ([c8e0983](https://github.com/nuxt-contrib/create-require/commit/c8e09834e322d8a106ac8018011f799e2fed03f2))

### [1.0.1](https://github.com/nuxt-contrib/create-require/compare/v1.0.0...v1.0.1) (2020-06-06)


### Bug Fixes

* **types:** specify types field ([2c06464](https://github.com/nuxt-contrib/create-require/commit/2c0646407704c1c534babdfed39a48f51fc4f616))

### 0.0.1 (2020-05-04)
